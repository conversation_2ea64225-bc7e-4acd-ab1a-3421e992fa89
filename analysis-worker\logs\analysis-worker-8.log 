{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","userEmail":"<EMAIL>","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:26","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:46","useBatch":true,"userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"batched":true,"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"batched":true,"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"batched":true,"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"batched":true,"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"batched":true,"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"batched":true,"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"batched":true,"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"batched":true,"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"batched":true,"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"batched":true,"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"info","message":"Assessment job processed successfully","processingTime":"82104ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"info","message":"Assessment job processed successfully","processingTime":"82104ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"info","message":"Assessment job processed successfully","processingTime":"82105ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"info","message":"Assessment job processed successfully","processingTime":"82100ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"info","message":"Assessment job processed successfully","processingTime":"82105ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"info","message":"Assessment job processed successfully","processingTime":"82105ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"info","message":"Assessment job processed successfully","processingTime":"82101ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"info","message":"Assessment job processed successfully","processingTime":"82106ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"info","message":"Assessment job processed successfully","processingTime":"82106ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:37","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:37","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:38","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:38","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:38","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:38","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:08","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:08","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Analysis result saved successfully","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Analysis result saved to Archive Service","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Analysis result saved successfully","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Analysis result saved to Archive Service","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"error","message":"Failed to update assessment job status","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Assessment job status updated","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"error","message":"Failed to update assessment job status","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Assessment job status updated","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Analysis complete notification sent","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Analysis completion notification sent","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"info","message":"Assessment job processed successfully","processingTime":"82238ms","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"77bdeb2f-47de-4b7e-96b9-9da0bdb620c3","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Analysis complete notification sent","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Analysis completion notification sent","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"info","message":"Assessment job processed successfully","processingTime":"82165ms","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e7ccedbd-24ec-4ebb-a0c5-9f80fb47ce99","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Analysis result saved successfully","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Analysis result saved to Archive Service","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Analysis result saved successfully","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Analysis result saved to Archive Service","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"error","message":"Failed to update assessment job status","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Assessment job status updated","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Analysis complete notification sent","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Analysis completion notification sent","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"info","message":"Assessment job processed successfully","processingTime":"81985ms","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b863b271-1148-4782-8447-2453b8c263db","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"error","message":"Failed to update assessment job status","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Assessment job status updated","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Analysis complete notification sent","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Analysis completion notification sent","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"info","message":"Assessment job processed successfully","processingTime":"81989ms","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"9dbac609-02d3-4181-b85b-b957d9bad7f1","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Analysis result saved successfully","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Analysis result saved to Archive Service","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Analysis result saved successfully","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Analysis result saved to Archive Service","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"error","message":"Failed to update assessment job status","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Assessment job status updated","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Analysis complete notification sent","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Analysis completion notification sent","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"info","message":"Assessment job processed successfully","processingTime":"82085ms","resultId":"9777b297-f339-440b-9e04-c2128835d610","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2162807a-4bc8-42d1-9478-3125e8fcb634","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"error","message":"Failed to update assessment job status","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Assessment job status updated","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Analysis complete notification sent","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Analysis completion notification sent","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"info","message":"Assessment job processed successfully","processingTime":"82006ms","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"a0aab098-2aa1-48bd-9466-edb72636c414","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Analysis result saved successfully","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Analysis result saved to Archive Service","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Analysis result saved successfully","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Analysis result saved to Archive Service","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"error","message":"Failed to update assessment job status","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Assessment job status updated","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Analysis complete notification sent","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Analysis completion notification sent","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"info","message":"Assessment job processed successfully","processingTime":"82053ms","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"5e6c031e-1440-4d3d-ade9-167e66d8219c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"error","message":"Failed to update assessment job status","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Assessment job status updated","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Analysis complete notification sent","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Analysis completion notification sent","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"info","message":"Assessment job processed successfully","processingTime":"81999ms","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"c306c245-0c97-42b8-888c-8d734a25b523","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Analysis result saved successfully","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Analysis result saved to Archive Service","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Analysis result saved successfully","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Analysis result saved to Archive Service","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"error","message":"Failed to update assessment job status","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Assessment job status updated","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Analysis complete notification sent","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Analysis completion notification sent","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"info","message":"Assessment job processed successfully","processingTime":"82150ms","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ec6a6061-9a64-4f6b-9bac-968d38af4b82","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"error","message":"Failed to update assessment job status","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Assessment job status updated","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Analysis complete notification sent","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Analysis completion notification sent","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"info","message":"Assessment job processed successfully","processingTime":"82078ms","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5870f946-a99c-417b-97fb-b4faedccfab4","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:38","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Analysis result saved successfully","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Analysis result saved to Archive Service","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Analysis result saved successfully","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Analysis result saved to Archive Service","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"error","message":"Failed to update assessment job status","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Assessment job status updated","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"error","message":"Failed to update assessment job status","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Assessment job status updated","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Analysis complete notification sent","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Analysis completion notification sent","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"info","message":"Assessment job processed successfully","processingTime":"82028ms","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"586e37a6-9049-44fa-8114-84bcb469a25d","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Analysis complete notification sent","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Analysis completion notification sent","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"info","message":"Assessment job processed successfully","processingTime":"82029ms","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c79483a3-2036-4c4c-bbd1-bd2ab235eb5c","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Analysis result saved successfully","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Analysis result saved to Archive Service","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Analysis result saved successfully","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Analysis result saved to Archive Service","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"error","message":"Failed to update assessment job status","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Assessment job status updated","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Analysis complete notification sent","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Analysis completion notification sent","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"info","message":"Assessment job processed successfully","processingTime":"82040ms","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"7c988082-bf92-43d6-9b4e-344b3e1663b9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"error","message":"Failed to update assessment job status","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Assessment job status updated","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Analysis complete notification sent","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Analysis completion notification sent","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"info","message":"Assessment job processed successfully","processingTime":"82034ms","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"16883910-d6b9-4ec4-86fd-fb50c15e25dd","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Analysis result saved successfully","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Analysis result saved to Archive Service","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"error","message":"Failed to update assessment job status","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Assessment job status updated","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Analysis complete notification sent","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Analysis completion notification sent","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"info","message":"Assessment job processed successfully","processingTime":"82025ms","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"feee5486-452a-4d73-998b-6449bbde615a","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:08","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:38","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Analysis result saved successfully","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Analysis result saved to Archive Service","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Analysis result saved successfully","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Analysis result saved to Archive Service","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"error","message":"Failed to update assessment job status","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Assessment job status updated","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"error","message":"Failed to update assessment job status","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Assessment job status updated","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Analysis complete notification sent","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Analysis completion notification sent","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"info","message":"Assessment job processed successfully","processingTime":"82055ms","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"909ba7b4-65aa-4d91-9d40-67f8f98f55fd","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Analysis complete notification sent","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Analysis completion notification sent","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"info","message":"Assessment job processed successfully","processingTime":"81981ms","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"43e2df13-5841-4f63-b34f-b15ed415dfe8","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Analysis result saved successfully","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Analysis result saved to Archive Service","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Analysis result saved successfully","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Analysis result saved to Archive Service","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"error","message":"Failed to update assessment job status","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Assessment job status updated","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"error","message":"Failed to update assessment job status","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Assessment job status updated","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Analysis complete notification sent","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Analysis completion notification sent","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"info","message":"Assessment job processed successfully","processingTime":"82146ms","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b04d55a1-f405-4aa7-974b-4f3cd89439ad","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Analysis complete notification sent","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Analysis completion notification sent","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"info","message":"Assessment job processed successfully","processingTime":"82048ms","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3bce52e9-c3ff-4ea0-afc7-9bc3928ede43","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","useMockModel":true,"version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Analysis result saved successfully","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Analysis result saved to Archive Service","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Analysis result saved successfully","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Analysis result saved to Archive Service","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"error","message":"Failed to update assessment job status","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Assessment job status updated","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Analysis complete notification sent","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Analysis completion notification sent","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"info","message":"Assessment job processed successfully","processingTime":"81982ms","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e9724ad5-c2ab-4021-859b-a64f450eeaa8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"error","message":"Failed to update assessment job status","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Assessment job status updated","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Analysis complete notification sent","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Analysis completion notification sent","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"info","message":"Assessment job processed successfully","processingTime":"81967ms","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"397766b9-0ccc-4e1b-8a0d-c295421c9b30","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Analysis result saved successfully","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Analysis result saved to Archive Service","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Analysis result saved successfully","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Analysis result saved to Archive Service","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"error","message":"Failed to update assessment job status","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Assessment job status updated","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Analysis complete notification sent","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Analysis completion notification sent","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"info","message":"Assessment job processed successfully","processingTime":"82066ms","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"56e1b1b8-8349-42d6-994f-188a81a62dd8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"error","message":"Failed to update assessment job status","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Assessment job status updated","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Analysis complete notification sent","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Analysis completion notification sent","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"info","message":"Assessment job processed successfully","processingTime":"82068ms","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"a0afc1d0-7a08-4839-90db-cba7767b2bf5","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Analysis result saved successfully","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Analysis result saved to Archive Service","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Analysis result saved successfully","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Analysis result saved to Archive Service","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"error","message":"Failed to update assessment job status","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Assessment job status updated","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Analysis complete notification sent","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Analysis completion notification sent","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"info","message":"Assessment job processed successfully","processingTime":"82080ms","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d34e6eb4-fcc7-4302-8817-039904bda976","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"error","message":"Failed to update assessment job status","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Assessment job status updated","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Analysis complete notification sent","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Analysis completion notification sent","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"info","message":"Assessment job processed successfully","processingTime":"82085ms","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"0f625e84-70b2-4289-84a4-a425b73a4e43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:38","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Analysis result saved successfully","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Analysis result saved to Archive Service","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Analysis result saved successfully","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Analysis result saved to Archive Service","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"error","message":"Failed to update assessment job status","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Assessment job status updated","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"error","message":"Failed to update assessment job status","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Assessment job status updated","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Analysis complete notification sent","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Analysis completion notification sent","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"info","message":"Assessment job processed successfully","processingTime":"82161ms","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"76c27b24-9a82-42f0-92df-0ba0f94507d6","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Analysis complete notification sent","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Analysis completion notification sent","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"info","message":"Assessment job processed successfully","processingTime":"82168ms","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"942eec5b-1206-4920-b697-109de1bb3356","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:09","version":"1.0.0","weaknessesCount":3}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Analysis result saved successfully","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:11","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Analysis result saved to Archive Service","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"error","message":"Failed to update assessment job status","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Assessment job status updated","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Analysis complete notification sent","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Analysis completion notification sent","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"info","message":"Assessment job processed successfully","processingTime":"82021ms","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3564678f-88df-4ebf-8069-5588da32e9df","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 05:45:02","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 05:45:03","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:45:03","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 05:45:03","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:45:03","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 05:45:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:05:03","version":"1.0.0"}
