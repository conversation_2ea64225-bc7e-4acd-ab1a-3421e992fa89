{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","userEmail":"<EMAIL>","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","useMockModel":true,"version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:44","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:14","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"archetype":"The Charismatic Leader","careerCount":5,"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Charismatic Leader","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"archetype":"The Strategic Organizer","careerCount":5,"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Organizer","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:34","version":"1.0.0","weaknessesCount":3}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useBatch":true,"userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"batched":true,"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"batched":true,"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"batched":true,"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"batched":true,"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"batched":true,"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"batched":true,"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"batched":true,"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"batched":true,"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"batched":true,"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"batched":true,"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"info","message":"Assessment job processed successfully","processingTime":"80356ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userEmail":"<EMAIL>","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userEmail":"<EMAIL>","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useMockModel":true,"version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"info","message":"Assessment job processed successfully","processingTime":"80359ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"info","message":"Assessment job processed successfully","processingTime":"80360ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"info","message":"Assessment job processed successfully","processingTime":"80361ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"info","message":"Assessment job processed successfully","processingTime":"80363ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"info","message":"Assessment job processed successfully","processingTime":"80365ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"info","message":"Assessment job processed successfully","processingTime":"80365ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"info","message":"Assessment job processed successfully","processingTime":"80366ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"info","message":"Assessment job processed successfully","processingTime":"80366ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"info","message":"Assessment job processed successfully","processingTime":"80369ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:44","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:45:54","version":"1.0.0","weaknessesCount":3}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:45:54","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:45:54","useBatch":true,"userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"batched":true,"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Analysis result saved successfully","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:45:56","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Analysis result saved to Archive Service","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"error","message":"Failed to update assessment job status","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:45:56","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Assessment job status updated","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Analysis complete notification sent","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Analysis completion notification sent","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"info","message":"Assessment job processed successfully","processingTime":"82033ms","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"0a749d3b-def6-4e0c-a6e1-f73aa1a01d00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:44","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:44","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:44","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:44","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:44","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:44","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:52:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:52:14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:52:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:26","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:26","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:26","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:26","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:26","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:53:56","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:56","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Analysis result saved successfully","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Analysis result saved to Archive Service","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Analysis result saved successfully","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Analysis result saved to Archive Service","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"error","message":"Failed to update assessment job status","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Assessment job status updated","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"error","message":"Failed to update assessment job status","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Assessment job status updated","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Analysis complete notification sent","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Analysis completion notification sent","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"info","message":"Assessment job processed successfully","processingTime":"82246ms","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b52776bf-7066-4955-9d06-ccbcdfc68dcf","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Analysis complete notification sent","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Analysis completion notification sent","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"info","message":"Assessment job processed successfully","processingTime":"82232ms","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2ad39062-284d-4cab-8d0c-c408039607ec","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Analysis result saved successfully","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Analysis result saved to Archive Service","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Analysis result saved successfully","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Analysis result saved to Archive Service","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"error","message":"Failed to update assessment job status","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Assessment job status updated","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Analysis complete notification sent","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Analysis completion notification sent","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"info","message":"Assessment job processed successfully","processingTime":"82065ms","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"2e6a4fdc-6dfe-4cbb-8e59-3cd19e6c9a34","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"error","message":"Failed to update assessment job status","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Assessment job status updated","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Analysis complete notification sent","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Analysis completion notification sent","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"info","message":"Assessment job processed successfully","processingTime":"81990ms","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65856ef9-2431-4f19-a086-24c7924b8c27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Analysis result saved successfully","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Analysis result saved to Archive Service","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Analysis result saved successfully","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Analysis result saved to Archive Service","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"error","message":"Failed to update assessment job status","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Assessment job status updated","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Analysis complete notification sent","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Analysis completion notification sent","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"info","message":"Assessment job processed successfully","processingTime":"82084ms","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2b07b5fd-65f3-4a71-9e69-4212d032897e","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"error","message":"Failed to update assessment job status","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Assessment job status updated","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Analysis complete notification sent","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Analysis completion notification sent","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"info","message":"Assessment job processed successfully","processingTime":"82089ms","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"bcc2dcc0-4d07-45f2-b98e-5636e822203a","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Analysis result saved successfully","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Analysis result saved to Archive Service","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Analysis result saved successfully","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Analysis result saved to Archive Service","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"error","message":"Failed to update assessment job status","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Assessment job status updated","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Analysis complete notification sent","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Analysis completion notification sent","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"info","message":"Assessment job processed successfully","processingTime":"82062ms","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"d78ef103-c450-452a-98ec-6dc937841993","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"error","message":"Failed to update assessment job status","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Assessment job status updated","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Analysis complete notification sent","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Analysis completion notification sent","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"info","message":"Assessment job processed successfully","processingTime":"82066ms","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"181591bf-b53c-406e-870c-275177096aeb","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Analysis result saved successfully","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Analysis result saved to Archive Service","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"error","message":"Failed to update assessment job status","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Assessment job status updated","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Analysis result saved successfully","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Analysis result saved to Archive Service","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Analysis complete notification sent","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Analysis completion notification sent","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"info","message":"Assessment job processed successfully","processingTime":"82087ms","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e16b1415-7e6e-49a6-ab9a-cd04b43f66c5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"error","message":"Failed to update assessment job status","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Assessment job status updated","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Analysis complete notification sent","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Analysis completion notification sent","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"info","message":"Assessment job processed successfully","processingTime":"82131ms","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"17db23cc-8269-435c-aa4a-bdafa4096b5f","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:26","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Analysis result saved successfully","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Analysis result saved to Archive Service","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Analysis result saved successfully","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Analysis result saved to Archive Service","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"error","message":"Failed to update assessment job status","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Assessment job status updated","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Analysis complete notification sent","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Analysis completion notification sent","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"info","message":"Assessment job processed successfully","processingTime":"82034ms","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"e942f916-3af7-48ad-9f4f-2c646422e0de","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"error","message":"Failed to update assessment job status","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Assessment job status updated","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Analysis complete notification sent","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Analysis completion notification sent","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"info","message":"Assessment job processed successfully","processingTime":"82037ms","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"64e35dab-da3a-4c68-ab6f-452cf86086a3","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Analysis result saved successfully","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Analysis result saved to Archive Service","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Analysis result saved successfully","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Analysis result saved to Archive Service","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"error","message":"Failed to update assessment job status","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Assessment job status updated","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Analysis complete notification sent","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Analysis completion notification sent","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"info","message":"Assessment job processed successfully","processingTime":"82040ms","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"6b75cc07-aab5-4810-88e2-4dfec327a93e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"error","message":"Failed to update assessment job status","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Assessment job status updated","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Analysis complete notification sent","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Analysis completion notification sent","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"info","message":"Assessment job processed successfully","processingTime":"82039ms","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4d48a9bd-0a75-4037-96b8-b7a64938e3ec","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Analysis result saved successfully","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Analysis result saved to Archive Service","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"error","message":"Failed to update assessment job status","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Assessment job status updated","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Analysis complete notification sent","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Analysis completion notification sent","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"info","message":"Assessment job processed successfully","processingTime":"82042ms","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"fa2e52b6-b358-4ae3-8a44-845407c0736e","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:56","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:26","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Analysis result saved successfully","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Analysis result saved to Archive Service","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Analysis result saved successfully","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Analysis result saved to Archive Service","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"error","message":"Failed to update assessment job status","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Assessment job status updated","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Analysis complete notification sent","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Analysis completion notification sent","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"info","message":"Assessment job processed successfully","processingTime":"82081ms","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c3a81a8a-5d13-45bc-9752-1e17082d1775","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"error","message":"Failed to update assessment job status","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Assessment job status updated","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Analysis complete notification sent","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Analysis completion notification sent","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"info","message":"Assessment job processed successfully","processingTime":"82014ms","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"6af60f3b-78ea-4c0b-b597-567886de3ff0","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Analysis result saved successfully","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Analysis result saved to Archive Service","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Analysis result saved successfully","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Analysis result saved to Archive Service","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"error","message":"Failed to update assessment job status","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Assessment job status updated","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Analysis complete notification sent","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Analysis completion notification sent","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"info","message":"Assessment job processed successfully","processingTime":"82113ms","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"abc61c68-1f14-46c5-a777-1bc5165311ed","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"error","message":"Failed to update assessment job status","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Assessment job status updated","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Analysis complete notification sent","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Analysis completion notification sent","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"info","message":"Assessment job processed successfully","processingTime":"82102ms","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"5bd0b0da-2cec-42a1-b28e-4e9b4415ca70","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Analysis result saved successfully","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Analysis result saved to Archive Service","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Analysis result saved successfully","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Analysis result saved to Archive Service","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"error","message":"Failed to update assessment job status","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Assessment job status updated","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Analysis complete notification sent","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Analysis completion notification sent","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"info","message":"Assessment job processed successfully","processingTime":"81970ms","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"134cd1ec-2c4b-496a-8c64-e0028304a6e2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"error","message":"Failed to update assessment job status","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Assessment job status updated","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Analysis complete notification sent","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Analysis completion notification sent","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"info","message":"Assessment job processed successfully","processingTime":"81973ms","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1652570d-d079-4e67-9557-9666d3df5d8e","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Analysis result saved successfully","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Analysis result saved to Archive Service","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Analysis result saved successfully","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Analysis result saved to Archive Service","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"error","message":"Failed to update assessment job status","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Assessment job status updated","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Analysis complete notification sent","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Analysis completion notification sent","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"info","message":"Assessment job processed successfully","processingTime":"82064ms","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"d05cd89d-022b-4446-a703-7bb5ad32aaf8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"error","message":"Failed to update assessment job status","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Assessment job status updated","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Analysis complete notification sent","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Analysis completion notification sent","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"info","message":"Assessment job processed successfully","processingTime":"82001ms","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b493bebc-02d8-4bbf-8b62-00710196f204","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:56","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Analysis result saved successfully","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Analysis result saved to Archive Service","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Analysis result saved successfully","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Analysis result saved to Archive Service","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"error","message":"Failed to update assessment job status","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Assessment job status updated","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Analysis complete notification sent","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Analysis completion notification sent","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"info","message":"Assessment job processed successfully","processingTime":"82075ms","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"d9650986-5061-4996-a3e5-2fabaea37da8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"error","message":"Failed to update assessment job status","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Assessment job status updated","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Analysis complete notification sent","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Analysis completion notification sent","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"info","message":"Assessment job processed successfully","processingTime":"82083ms","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b80d7c69-0030-4e28-8a37-d4a77075fbb3","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:56","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Analysis result saved successfully","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Analysis result saved to Archive Service","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Analysis result saved successfully","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Analysis result saved to Archive Service","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"error","message":"Failed to update assessment job status","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Assessment job status updated","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"error","message":"Failed to update assessment job status","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Assessment job status updated","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Analysis complete notification sent","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Analysis completion notification sent","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"info","message":"Assessment job processed successfully","processingTime":"82142ms","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d72b1129-0869-4978-a53a-15c66df55aaa","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Analysis complete notification sent","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Analysis completion notification sent","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"info","message":"Assessment job processed successfully","processingTime":"82139ms","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"581aedee-4aa4-49f2-9144-2cac5b9b845c","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 05:44:50","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 05:44:50","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:50","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 05:44:50","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:50","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 05:44:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:21","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 06:01:40","userEmail":"<EMAIL>","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 06:01:40","userEmail":"<EMAIL>","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 06:01:40","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 06:01:40","useMockModel":true,"version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 06:01:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:51","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 06:03:00","version":"1.0.0","weaknessesCount":3}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 06:03:00","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 06:03:00","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Analysis result saved successfully","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","status":201,"timestamp":"2025-07-19 06:03:02","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Analysis result saved to Archive Service","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","timestamp":"2025-07-19 06:03:02","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"error","message":"Failed to update assessment job status","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 06:03:02","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Assessment job status updated","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","timestamp":"2025-07-19 06:03:02","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Analysis complete notification sent","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","timestamp":"2025-07-19 06:03:02","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Analysis completion notification sent","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","timestamp":"2025-07-19 06:03:02","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"info","message":"Assessment job processed successfully","processingTime":"82065ms","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 06:03:02","userId":"112713a7-b0c1-406d-a9b8-b7f07b92656a","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:51","version":"1.0.0"}
