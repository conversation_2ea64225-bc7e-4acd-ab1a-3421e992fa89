{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","userEmail":"<EMAIL>","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:22","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","useBatch":true,"userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"batched":true,"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"batched":true,"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"batched":true,"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"batched":true,"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"batched":true,"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"batched":true,"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"batched":true,"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"batched":true,"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"batched":true,"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"batched":true,"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"info","message":"Assessment job processed successfully","processingTime":"82137ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"info","message":"Assessment job processed successfully","processingTime":"82137ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"info","message":"Assessment job processed successfully","processingTime":"82137ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"info","message":"Assessment job processed successfully","processingTime":"82136ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"info","message":"Assessment job processed successfully","processingTime":"82142ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"info","message":"Assessment job processed successfully","processingTime":"82142ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"info","message":"Assessment job processed successfully","processingTime":"82139ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"info","message":"Assessment job processed successfully","processingTime":"82143ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"info","message":"Assessment job processed successfully","processingTime":"82143ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"info","message":"Assessment job processed successfully","processingTime":"82144ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:34","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:34","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:34","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:34","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:34","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:34","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:04","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Analysis result saved successfully","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Analysis result saved to Archive Service","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Analysis result saved successfully","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Analysis result saved to Archive Service","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"error","message":"Failed to update assessment job status","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Assessment job status updated","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"error","message":"Failed to update assessment job status","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Assessment job status updated","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Analysis complete notification sent","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Analysis completion notification sent","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"info","message":"Assessment job processed successfully","processingTime":"82259ms","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a92ef208-7542-42bd-9f7e-ea5fbb23d387","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Analysis complete notification sent","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Analysis completion notification sent","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"info","message":"Assessment job processed successfully","processingTime":"82219ms","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"3ecaf01f-b7eb-4b33-9fb3-d5bb9f32f4d9","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Analysis result saved successfully","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Analysis result saved to Archive Service","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Analysis result saved successfully","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Analysis result saved to Archive Service","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"68614478-de78-4918-bc02-4427e147f293","level":"error","message":"Failed to update assessment job status","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Assessment job status updated","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Analysis complete notification sent","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Analysis completion notification sent","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"jobId":"68614478-de78-4918-bc02-4427e147f293","level":"info","message":"Assessment job processed successfully","processingTime":"81991ms","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"eadf89c1-8bb3-46fb-aa01-4f10732d81bf","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"error","message":"Failed to update assessment job status","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Assessment job status updated","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Analysis complete notification sent","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Analysis completion notification sent","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"info","message":"Assessment job processed successfully","processingTime":"81981ms","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5b692a64-458a-4178-ad62-77f9049307ef","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Analysis result saved successfully","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Analysis result saved to Archive Service","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Analysis result saved successfully","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Analysis result saved to Archive Service","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"error","message":"Failed to update assessment job status","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Assessment job status updated","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Analysis complete notification sent","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Analysis completion notification sent","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"info","message":"Assessment job processed successfully","processingTime":"82115ms","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"96b805e2-af36-4048-ac55-be273ae09c55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"error","message":"Failed to update assessment job status","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Assessment job status updated","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Analysis complete notification sent","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Analysis completion notification sent","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"info","message":"Assessment job processed successfully","processingTime":"82086ms","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"2a81fd06-223c-442f-91b1-e9438fde7b92","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Analysis result saved successfully","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Analysis result saved to Archive Service","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Analysis result saved successfully","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Analysis result saved to Archive Service","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"error","message":"Failed to update assessment job status","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Assessment job status updated","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Analysis complete notification sent","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Analysis completion notification sent","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"info","message":"Assessment job processed successfully","processingTime":"82055ms","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"8ea8b5fb-18eb-4d1e-9a06-c5b5ce794edf","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"error","message":"Failed to update assessment job status","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Assessment job status updated","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Analysis complete notification sent","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Analysis completion notification sent","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"info","message":"Assessment job processed successfully","processingTime":"82000ms","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"76c3e746-5f63-4cc0-ac91-98ca70537e1f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Analysis result saved successfully","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Analysis result saved to Archive Service","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Analysis result saved successfully","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Analysis result saved to Archive Service","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"error","message":"Failed to update assessment job status","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Assessment job status updated","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Analysis complete notification sent","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Analysis completion notification sent","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"info","message":"Assessment job processed successfully","processingTime":"82106ms","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"6b7b8d14-b55b-4fa9-80e3-8aada8e8e108","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"error","message":"Failed to update assessment job status","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Assessment job status updated","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Analysis complete notification sent","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Analysis completion notification sent","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"info","message":"Assessment job processed successfully","processingTime":"82110ms","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"dfb339ad-f2a5-40ac-b846-9cd434aecb7c","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:34","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Analysis result saved successfully","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Analysis result saved to Archive Service","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Analysis result saved successfully","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Analysis result saved to Archive Service","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"error","message":"Failed to update assessment job status","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Assessment job status updated","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"error","message":"Failed to update assessment job status","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Assessment job status updated","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Analysis complete notification sent","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Analysis completion notification sent","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"info","message":"Assessment job processed successfully","processingTime":"82045ms","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"0ef1ffeb-a2f1-41cb-baa9-e182419fc743","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Analysis complete notification sent","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Analysis completion notification sent","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"info","message":"Assessment job processed successfully","processingTime":"82045ms","resultId":"374de024-4c4b-4565-9de3-24324c59f226","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"040622bf-c900-4837-ab45-8b1f6e8f12e1","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Analysis result saved successfully","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Analysis result saved to Archive Service","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Analysis result saved successfully","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Analysis result saved to Archive Service","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"error","message":"Failed to update assessment job status","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Assessment job status updated","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Analysis complete notification sent","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Analysis completion notification sent","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"info","message":"Assessment job processed successfully","processingTime":"82046ms","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"b6ee5ee9-c095-4688-aded-414c4a94157d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"error","message":"Failed to update assessment job status","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Assessment job status updated","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Analysis complete notification sent","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Analysis completion notification sent","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"info","message":"Assessment job processed successfully","processingTime":"82043ms","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"09561e7c-abec-4558-a1c4-db0b4a685839","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:34","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:34","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Analysis result saved successfully","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Analysis result saved to Archive Service","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Analysis result saved successfully","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Analysis result saved to Archive Service","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"error","message":"Failed to update assessment job status","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Assessment job status updated","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"error","message":"Failed to update assessment job status","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Assessment job status updated","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Analysis complete notification sent","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Analysis completion notification sent","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"info","message":"Assessment job processed successfully","processingTime":"82087ms","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"502d03bb-3ed8-48d1-b731-793b16b9e045","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Analysis complete notification sent","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Analysis completion notification sent","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"info","message":"Assessment job processed successfully","processingTime":"82042ms","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"52c0b64f-af1d-4df6-b53a-110d9efe92ec","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","useMockModel":true,"version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","useMockModel":true,"version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Analysis result saved successfully","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Analysis result saved to Archive Service","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Analysis result saved successfully","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Analysis result saved to Archive Service","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"error","message":"Failed to update assessment job status","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Assessment job status updated","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Analysis complete notification sent","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Analysis completion notification sent","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"info","message":"Assessment job processed successfully","processingTime":"82182ms","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"cb9ede11-0b68-422a-8a10-c5a511ae9cd9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"error","message":"Failed to update assessment job status","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Assessment job status updated","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Analysis complete notification sent","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Analysis completion notification sent","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"info","message":"Assessment job processed successfully","processingTime":"82084ms","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"3f093fdf-1873-4f15-a921-a637bc5e816d","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Analysis result saved successfully","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Analysis result saved to Archive Service","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Analysis result saved successfully","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Analysis result saved to Archive Service","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"error","message":"Failed to update assessment job status","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Assessment job status updated","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Analysis complete notification sent","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Analysis completion notification sent","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"info","message":"Assessment job processed successfully","processingTime":"82061ms","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7c9a4461-f767-41c1-a0c8-bd7c997d4fda","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"error","message":"Failed to update assessment job status","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Assessment job status updated","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Analysis complete notification sent","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Analysis completion notification sent","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"info","message":"Assessment job processed successfully","processingTime":"81970ms","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"62573eb1-b85e-4c72-8d51-bba16036d9f8","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Analysis result saved successfully","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Analysis result saved to Archive Service","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Analysis result saved successfully","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Analysis result saved to Archive Service","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"error","message":"Failed to update assessment job status","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Assessment job status updated","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Analysis complete notification sent","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Analysis completion notification sent","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"info","message":"Assessment job processed successfully","processingTime":"82067ms","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"8a0344b5-0c26-45bf-8096-325f9ee74785","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"error","message":"Failed to update assessment job status","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Assessment job status updated","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Analysis complete notification sent","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Analysis completion notification sent","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"info","message":"Assessment job processed successfully","processingTime":"82067ms","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"2af2f2a7-d448-4169-9994-fc74a93a483f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"archetype":"The Strategic Organizer","careerCount":5,"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Organizer","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Analysis result saved successfully","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Analysis result saved to Archive Service","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Analysis result saved successfully","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Analysis result saved to Archive Service","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"error","message":"Failed to update assessment job status","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Assessment job status updated","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Analysis complete notification sent","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Analysis completion notification sent","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"info","message":"Assessment job processed successfully","processingTime":"82110ms","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cb20b47f-5408-4183-9f35-7d9c5f57c95a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"error","message":"Failed to update assessment job status","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Assessment job status updated","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Analysis complete notification sent","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Analysis completion notification sent","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"info","message":"Assessment job processed successfully","processingTime":"82102ms","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"22801819-743a-4118-ac07-92695beff0c4","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:04","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:05","version":"1.0.0","weaknessesCount":3}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Analysis result saved successfully","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Analysis result saved to Archive Service","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Analysis result saved successfully","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Analysis result saved to Archive Service","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"error","message":"Failed to update assessment job status","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Assessment job status updated","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"error","message":"Failed to update assessment job status","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Assessment job status updated","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Analysis complete notification sent","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Analysis completion notification sent","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"info","message":"Assessment job processed successfully","processingTime":"82153ms","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"361545db-30ea-4be7-af9b-6f3484dcfbac","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Analysis complete notification sent","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Analysis completion notification sent","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"info","message":"Assessment job processed successfully","processingTime":"82149ms","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"cbd5ec6c-34e4-4780-8fe7-fb216365de8d","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:34","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 05:44:58","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 05:44:58","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:59","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 05:44:59","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:59","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 05:44:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:59","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:59","version":"1.0.0"}
