{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","userEmail":"<EMAIL>","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:20","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:40","useBatch":true,"userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"batched":true,"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"batched":true,"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"batched":true,"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"batched":true,"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"batched":true,"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"batched":true,"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"batched":true,"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"batched":true,"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"batched":true,"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"batched":true,"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"info","message":"Assessment job processed successfully","processingTime":"82086ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"info","message":"Assessment job processed successfully","processingTime":"82088ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"info","message":"Assessment job processed successfully","processingTime":"82087ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"info","message":"Assessment job processed successfully","processingTime":"82084ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"info","message":"Assessment job processed successfully","processingTime":"82091ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"info","message":"Assessment job processed successfully","processingTime":"82091ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"info","message":"Assessment job processed successfully","processingTime":"82091ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"info","message":"Assessment job processed successfully","processingTime":"82091ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"info","message":"Assessment job processed successfully","processingTime":"82092ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"info","message":"Assessment job processed successfully","processingTime":"82091ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:52:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:31","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:31","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:32","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:32","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:32","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:32","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:02","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:02","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Analysis result saved successfully","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Analysis result saved to Archive Service","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Analysis result saved successfully","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Analysis result saved to Archive Service","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"error","message":"Failed to update assessment job status","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Assessment job status updated","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"error","message":"Failed to update assessment job status","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Assessment job status updated","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Analysis complete notification sent","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Analysis completion notification sent","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"info","message":"Assessment job processed successfully","processingTime":"82213ms","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"91b2f9f6-1fc4-444b-acb7-3b993127d730","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Analysis complete notification sent","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Analysis completion notification sent","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"info","message":"Assessment job processed successfully","processingTime":"82173ms","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"fc11a0f5-8952-48fc-bd10-bf5101fb9412","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Analysis result saved successfully","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Analysis result saved to Archive Service","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Analysis result saved successfully","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Analysis result saved to Archive Service","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"error","message":"Failed to update assessment job status","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Assessment job status updated","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Analysis complete notification sent","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Analysis completion notification sent","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"info","message":"Assessment job processed successfully","processingTime":"81996ms","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"b8c61f72-657e-4fe5-85d0-0d7f1ec363cd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"error","message":"Failed to update assessment job status","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Assessment job status updated","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Analysis complete notification sent","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Analysis completion notification sent","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"info","message":"Assessment job processed successfully","processingTime":"81985ms","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ca9b8721-870f-4204-a48a-2683b1aac0ea","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Analysis result saved successfully","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Analysis result saved to Archive Service","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Analysis result saved successfully","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Analysis result saved to Archive Service","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"error","message":"Failed to update assessment job status","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Assessment job status updated","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Analysis complete notification sent","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Analysis completion notification sent","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"dd3f0da8-74db-4f45-9795-1c8a6b08cde4","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"error","message":"Failed to update assessment job status","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Assessment job status updated","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Analysis complete notification sent","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Analysis completion notification sent","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"info","message":"Assessment job processed successfully","processingTime":"82085ms","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"399d8dfc-f8f2-44c6-8dd7-ab81e0c35591","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Analysis result saved successfully","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Analysis result saved to Archive Service","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Analysis result saved successfully","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Analysis result saved to Archive Service","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"error","message":"Failed to update assessment job status","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Assessment job status updated","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Analysis complete notification sent","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Analysis completion notification sent","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"info","message":"Assessment job processed successfully","processingTime":"82039ms","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"7ac6d46d-ca30-4791-9a57-894b3675af40","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"error","message":"Failed to update assessment job status","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Assessment job status updated","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Analysis complete notification sent","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Analysis completion notification sent","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"info","message":"Assessment job processed successfully","processingTime":"81992ms","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dfb07e8-8120-4823-b5c1-fae36ba6dd09","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Analysis result saved successfully","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Analysis result saved to Archive Service","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Analysis result saved successfully","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Analysis result saved to Archive Service","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"error","message":"Failed to update assessment job status","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Assessment job status updated","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Analysis complete notification sent","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Analysis completion notification sent","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"info","message":"Assessment job processed successfully","processingTime":"82088ms","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"4ef60e5d-337a-431a-b71e-0e7b826ed819","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"error","message":"Failed to update assessment job status","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Assessment job status updated","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Analysis complete notification sent","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Analysis completion notification sent","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"info","message":"Assessment job processed successfully","processingTime":"82120ms","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c341f5a8-217b-48e0-a10d-bd8b11a8d4e7","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:32","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Analysis result saved successfully","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Analysis result saved to Archive Service","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Analysis result saved successfully","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Analysis result saved to Archive Service","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"error","message":"Failed to update assessment job status","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Assessment job status updated","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"error","message":"Failed to update assessment job status","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Assessment job status updated","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Analysis complete notification sent","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Analysis completion notification sent","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"info","message":"Assessment job processed successfully","processingTime":"82059ms","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c417bcd5-6fd1-4817-a013-3cd4da061d70","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Analysis complete notification sent","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Analysis completion notification sent","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"info","message":"Assessment job processed successfully","processingTime":"82054ms","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5a701200-0b5e-486c-ae32-a3a2eaf3e2cb","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Analysis result saved successfully","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Analysis result saved to Archive Service","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Analysis result saved successfully","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Analysis result saved to Archive Service","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"error","message":"Failed to update assessment job status","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Assessment job status updated","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Analysis complete notification sent","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Analysis completion notification sent","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"info","message":"Assessment job processed successfully","processingTime":"82043ms","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d7eca7ed-748c-4f76-8fad-7b5b9be46517","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"error","message":"Failed to update assessment job status","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Assessment job status updated","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Analysis complete notification sent","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Analysis completion notification sent","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"info","message":"Assessment job processed successfully","processingTime":"82054ms","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"d10f6c08-c103-4edc-af9c-32566b69da81","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Analysis result saved successfully","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Analysis result saved to Archive Service","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"error","message":"Failed to update assessment job status","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Assessment job status updated","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Analysis complete notification sent","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Analysis completion notification sent","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"info","message":"Assessment job processed successfully","processingTime":"82017ms","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"43566b77-24ed-43b4-ae1f-5765177ffdd1","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:02","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:32","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:32","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Analysis result saved successfully","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Analysis result saved to Archive Service","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Analysis result saved successfully","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Analysis result saved to Archive Service","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"error","message":"Failed to update assessment job status","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Assessment job status updated","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"error","message":"Failed to update assessment job status","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Assessment job status updated","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Analysis complete notification sent","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Analysis completion notification sent","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"info","message":"Assessment job processed successfully","processingTime":"82084ms","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"9a28085a-a0a6-4c30-9255-de98c320bbfd","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Analysis complete notification sent","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Analysis completion notification sent","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"info","message":"Assessment job processed successfully","processingTime":"82004ms","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"bb3e793d-600d-428d-a80f-d3e322743d16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Analysis result saved successfully","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Analysis result saved to Archive Service","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Analysis result saved successfully","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Analysis result saved to Archive Service","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"error","message":"Failed to update assessment job status","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Assessment job status updated","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Analysis complete notification sent","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Analysis completion notification sent","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"info","message":"Assessment job processed successfully","processingTime":"82139ms","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"275757b6-2084-4a7d-99d5-c48a11fbd152","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","useMockModel":true,"version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"error","message":"Failed to update assessment job status","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Assessment job status updated","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Analysis complete notification sent","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Analysis completion notification sent","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"info","message":"Assessment job processed successfully","processingTime":"82060ms","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"25f149f5-12ba-4fe8-a11f-5cd8fd36878b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Analysis result saved successfully","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Analysis result saved to Archive Service","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Analysis result saved successfully","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Analysis result saved to Archive Service","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"error","message":"Failed to update assessment job status","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Assessment job status updated","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Analysis complete notification sent","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Analysis completion notification sent","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"info","message":"Assessment job processed successfully","processingTime":"81978ms","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"a6a6336d-6855-47aa-98c8-069b921b3436","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"error","message":"Failed to update assessment job status","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Assessment job status updated","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Analysis complete notification sent","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Analysis completion notification sent","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"info","message":"Assessment job processed successfully","processingTime":"81964ms","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"1a878a52-004c-43e3-8228-fe04268d6662","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Analysis result saved successfully","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Analysis result saved to Archive Service","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Analysis result saved successfully","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Analysis result saved to Archive Service","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"error","message":"Failed to update assessment job status","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Assessment job status updated","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Analysis complete notification sent","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Analysis completion notification sent","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"info","message":"Assessment job processed successfully","processingTime":"82064ms","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"7aa43eec-8dbc-4d86-ac3a-9b15b707ada1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"error","message":"Failed to update assessment job status","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Assessment job status updated","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Analysis complete notification sent","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Analysis completion notification sent","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"info","message":"Assessment job processed successfully","processingTime":"82066ms","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"4f54020d-cd64-4feb-aaea-d51fc4337383","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Analysis result saved successfully","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Analysis result saved to Archive Service","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Analysis result saved successfully","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Analysis result saved to Archive Service","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"error","message":"Failed to update assessment job status","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Assessment job status updated","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Analysis complete notification sent","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Analysis completion notification sent","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"info","message":"Assessment job processed successfully","processingTime":"82087ms","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"a4a12f8d-8517-4164-ab2c-5b247fce4697","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"error","message":"Failed to update assessment job status","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Assessment job status updated","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Analysis complete notification sent","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Analysis completion notification sent","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"info","message":"Assessment job processed successfully","processingTime":"82077ms","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"4424be2a-053b-47f9-96aa-aa829c0767cb","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:02","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Analysis result saved successfully","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Analysis result saved to Archive Service","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Analysis result saved successfully","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Analysis result saved to Archive Service","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"error","message":"Failed to update assessment job status","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Assessment job status updated","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"error","message":"Failed to update assessment job status","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Assessment job status updated","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Analysis complete notification sent","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Analysis completion notification sent","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"info","message":"Assessment job processed successfully","processingTime":"82072ms","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"d044994e-a80f-4324-a1dd-58c57c191bf9","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Analysis complete notification sent","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Analysis completion notification sent","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"info","message":"Assessment job processed successfully","processingTime":"82079ms","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"90e5cf91-2958-44bd-b826-0277fa0e54c3","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:02:09","version":"1.0.0","weaknessesCount":3}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Analysis result saved successfully","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:11","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Analysis result saved to Archive Service","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"error","message":"Failed to update assessment job status","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Assessment job status updated","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Analysis complete notification sent","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Analysis completion notification sent","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"info","message":"Assessment job processed successfully","processingTime":"82055ms","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"1506215a-a8c6-4913-b790-91729ea42452","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:15:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 05:44:56","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 05:44:56","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:57","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 05:44:57","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:57","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 05:44:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:57","version":"1.0.0"}
