{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","userEmail":"<EMAIL>","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:30","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":4}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","useBatch":true,"userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"batched":true,"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"batched":true,"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"batched":true,"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"batched":true,"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"batched":true,"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"batched":true,"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"batched":true,"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"batched":true,"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"batched":true,"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"batched":true,"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"info","message":"Assessment job processed successfully","processingTime":"82105ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"info","message":"Assessment job processed successfully","processingTime":"82099ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"info","message":"Assessment job processed successfully","processingTime":"82106ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"info","message":"Assessment job processed successfully","processingTime":"82106ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"info","message":"Assessment job processed successfully","processingTime":"82106ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:52:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:41","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:41","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:41","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:41","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:41","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:41","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:11","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Analysis result saved successfully","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Analysis result saved to Archive Service","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Analysis result saved successfully","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Analysis result saved to Archive Service","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"error","message":"Failed to update assessment job status","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Assessment job status updated","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"error","message":"Failed to update assessment job status","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Assessment job status updated","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Analysis complete notification sent","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Analysis completion notification sent","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"info","message":"Assessment job processed successfully","processingTime":"82249ms","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f0babae7-0af2-4fb8-936d-4fe119200ec2","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Analysis complete notification sent","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Analysis completion notification sent","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"info","message":"Assessment job processed successfully","processingTime":"82161ms","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"46455869-970a-4ef9-aa37-deedba21a7c4","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Analysis result saved successfully","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Analysis result saved to Archive Service","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Analysis result saved successfully","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Analysis result saved to Archive Service","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"error","message":"Failed to update assessment job status","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Assessment job status updated","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Analysis complete notification sent","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Analysis completion notification sent","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"info","message":"Assessment job processed successfully","processingTime":"81986ms","resultId":"52000f6e-a553-45c3-9e39-da0552248375","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"59d6a7e0-f891-4f7c-aeec-4ec9e870ff99","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"error","message":"Failed to update assessment job status","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Assessment job status updated","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Analysis complete notification sent","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Analysis completion notification sent","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"info","message":"Assessment job processed successfully","processingTime":"81993ms","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"8e9dd8b0-0cc3-46d7-87d0-de0ceea9cb7c","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Analysis result saved successfully","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Analysis result saved to Archive Service","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Analysis result saved successfully","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Analysis result saved to Archive Service","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"error","message":"Failed to update assessment job status","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Assessment job status updated","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Analysis complete notification sent","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Analysis completion notification sent","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"info","message":"Assessment job processed successfully","processingTime":"82083ms","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d77063c7-07b3-4f36-850d-7caed3e113b5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"error","message":"Failed to update assessment job status","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Assessment job status updated","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Analysis complete notification sent","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Analysis completion notification sent","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"info","message":"Assessment job processed successfully","processingTime":"81943ms","resultId":"4786db40-a262-4430-a85b-7d214ed81214","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"ecadfc35-66a3-4c64-a2fd-4e93bfcccbdf","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Analysis result saved successfully","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Analysis result saved to Archive Service","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Analysis result saved successfully","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Analysis result saved to Archive Service","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"error","message":"Failed to update assessment job status","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Assessment job status updated","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Analysis complete notification sent","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Analysis completion notification sent","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"info","message":"Assessment job processed successfully","processingTime":"82034ms","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"388b9e4d-b716-4c08-9b5b-103769b8ec30","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"error","message":"Failed to update assessment job status","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Assessment job status updated","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Analysis complete notification sent","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Analysis completion notification sent","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"info","message":"Assessment job processed successfully","processingTime":"81988ms","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"a3043cc7-4541-4b17-8713-389da6e5b3df","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Analysis result saved successfully","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Analysis result saved to Archive Service","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Analysis result saved successfully","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Analysis result saved to Archive Service","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"error","message":"Failed to update assessment job status","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Assessment job status updated","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Analysis complete notification sent","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Analysis completion notification sent","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"info","message":"Assessment job processed successfully","processingTime":"82094ms","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ca414959-1b51-4aee-bc50-5dc52cf19f41","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"error","message":"Failed to update assessment job status","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Assessment job status updated","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Analysis complete notification sent","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Analysis completion notification sent","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"info","message":"Assessment job processed successfully","processingTime":"82001ms","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"a5a9acfc-715e-4667-82a2-c4fc1ed560f7","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:11","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:41","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Analysis result saved successfully","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Analysis result saved to Archive Service","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Analysis result saved successfully","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Analysis result saved to Archive Service","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"error","message":"Failed to update assessment job status","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Assessment job status updated","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"error","message":"Failed to update assessment job status","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Assessment job status updated","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Analysis complete notification sent","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Analysis completion notification sent","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"info","message":"Assessment job processed successfully","processingTime":"82036ms","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"40f6b61c-eb81-4253-ae3c-bcab41409aba","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Analysis complete notification sent","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Analysis completion notification sent","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"info","message":"Assessment job processed successfully","processingTime":"82037ms","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"d58a23ff-387b-4cc7-8a7e-6af256a40c14","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Analysis result saved successfully","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Analysis result saved to Archive Service","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Analysis result saved successfully","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Analysis result saved to Archive Service","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"error","message":"Failed to update assessment job status","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Assessment job status updated","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Analysis complete notification sent","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Analysis completion notification sent","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"info","message":"Assessment job processed successfully","processingTime":"82038ms","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"5553c3e9-acea-43a4-a2c2-2ceaf308bbce","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"error","message":"Failed to update assessment job status","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Assessment job status updated","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Analysis complete notification sent","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Analysis completion notification sent","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"info","message":"Assessment job processed successfully","processingTime":"82040ms","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"6f3623c2-ce7e-478e-9649-4e0c04f4afe1","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:42","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Analysis result saved successfully","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Analysis result saved to Archive Service","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Analysis result saved successfully","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Analysis result saved to Archive Service","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"error","message":"Failed to update assessment job status","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Assessment job status updated","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"error","message":"Failed to update assessment job status","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Assessment job status updated","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Analysis complete notification sent","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Analysis completion notification sent","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"info","message":"Assessment job processed successfully","processingTime":"82072ms","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"0af27be6-c685-4ef4-bb13-962c7445fddf","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Analysis complete notification sent","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Analysis completion notification sent","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"info","message":"Assessment job processed successfully","processingTime":"81996ms","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"1fbf7c5e-8f9b-4bfb-a599-e35dfe92f901","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Analysis result saved successfully","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Analysis result saved to Archive Service","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Analysis result saved successfully","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Analysis result saved to Archive Service","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"error","message":"Failed to update assessment job status","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Assessment job status updated","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Analysis complete notification sent","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Analysis completion notification sent","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"info","message":"Assessment job processed successfully","processingTime":"82123ms","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"73cac304-80c1-4351-8527-085332f32573","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","useMockModel":true,"version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"error","message":"Failed to update assessment job status","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Assessment job status updated","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Analysis complete notification sent","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Analysis completion notification sent","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"info","message":"Assessment job processed successfully","processingTime":"82064ms","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"04626480-3e83-4f93-b43a-29224ef18fae","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Analysis result saved successfully","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Analysis result saved to Archive Service","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Analysis result saved successfully","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Analysis result saved to Archive Service","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"error","message":"Failed to update assessment job status","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Assessment job status updated","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Analysis complete notification sent","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Analysis completion notification sent","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"info","message":"Assessment job processed successfully","processingTime":"82065ms","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"c664a241-7834-4ef7-9ba6-3a857ee13939","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"error","message":"Failed to update assessment job status","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Assessment job status updated","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Analysis complete notification sent","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Analysis completion notification sent","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"info","message":"Assessment job processed successfully","processingTime":"81974ms","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7718b3c2-ccbe-4682-9c45-b70c6e9b2e38","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Analysis result saved successfully","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Analysis result saved to Archive Service","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Analysis result saved successfully","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Analysis result saved to Archive Service","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"error","message":"Failed to update assessment job status","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Assessment job status updated","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Analysis complete notification sent","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Analysis completion notification sent","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"info","message":"Assessment job processed successfully","processingTime":"82064ms","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"fef56168-c989-4af9-b1c1-8bf7af70a316","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"error","message":"Failed to update assessment job status","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Assessment job status updated","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Analysis complete notification sent","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Analysis completion notification sent","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"info","message":"Assessment job processed successfully","processingTime":"82062ms","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"46ff610d-e4c4-4ae5-b6ec-58a30d831b7d","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Analysis result saved successfully","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Analysis result saved to Archive Service","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Analysis result saved successfully","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Analysis result saved to Archive Service","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"error","message":"Failed to update assessment job status","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Assessment job status updated","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Analysis complete notification sent","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Analysis completion notification sent","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"info","message":"Assessment job processed successfully","processingTime":"82084ms","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"8e4bf335-66d4-462e-82a3-9cf1857325f4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"error","message":"Failed to update assessment job status","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Assessment job status updated","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Analysis complete notification sent","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Analysis completion notification sent","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"info","message":"Assessment job processed successfully","processingTime":"82097ms","resultId":"f18db25e-67cc-4838-95e8-02576915156c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"fc1ca56e-e003-4d38-adf5-b2acc8e102df","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:42","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Analysis result saved successfully","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Analysis result saved to Archive Service","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Analysis result saved successfully","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Analysis result saved to Archive Service","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"error","message":"Failed to update assessment job status","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Assessment job status updated","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"error","message":"Failed to update assessment job status","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Assessment job status updated","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Analysis complete notification sent","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Analysis completion notification sent","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"info","message":"Assessment job processed successfully","processingTime":"82116ms","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"056e5300-9994-4854-b3a3-2a14fd243d55","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Analysis complete notification sent","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Analysis completion notification sent","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"info","message":"Assessment job processed successfully","processingTime":"82123ms","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2336fd8a-e36f-4201-ad70-38cfec20c9fa","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:09","version":"1.0.0","weaknessesCount":3}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Analysis result saved successfully","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:11","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Analysis result saved to Archive Service","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"error","message":"Failed to update assessment job status","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Assessment job status updated","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Analysis complete notification sent","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Analysis completion notification sent","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"info","message":"Assessment job processed successfully","processingTime":"82060ms","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"3e7dcb6b-7e00-48e8-9d66-3cb78122c1f4","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 05:45:07","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 05:45:07","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:45:07","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 05:45:07","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:45:07","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 05:45:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:05:07","version":"1.0.0"}
