{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","userEmail":"<EMAIL>","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","useMockModel":true,"version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:46","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:16","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:36","version":"1.0.0","weaknessesCount":3}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 04:44:36","useBatch":true,"userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"batched":true,"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"batched":true,"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"batched":true,"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"batched":true,"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"batched":true,"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"batched":true,"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"batched":true,"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"batched":true,"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"batched":true,"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"batched":true,"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"info","message":"Assessment job processed successfully","processingTime":"82125ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"info","message":"Assessment job processed successfully","processingTime":"82126ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"info","message":"Assessment job processed successfully","processingTime":"82126ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"info","message":"Assessment job processed successfully","processingTime":"82121ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"info","message":"Assessment job processed successfully","processingTime":"82128ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"info","message":"Assessment job processed successfully","processingTime":"82128ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"info","message":"Assessment job processed successfully","processingTime":"82128ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"info","message":"Assessment job processed successfully","processingTime":"82129ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"info","message":"Assessment job processed successfully","processingTime":"82130ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"info","message":"Assessment job processed successfully","processingTime":"82130ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:46","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:46","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:52:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:52:16","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:52:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:27","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:27","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:27","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:27","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:27","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:53:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:58","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Analysis result saved successfully","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Analysis result saved to Archive Service","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Analysis result saved successfully","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Analysis result saved to Archive Service","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"error","message":"Failed to update assessment job status","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Assessment job status updated","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"error","message":"Failed to update assessment job status","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Assessment job status updated","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Analysis complete notification sent","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Analysis completion notification sent","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"info","message":"Assessment job processed successfully","processingTime":"82240ms","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"1344573b-3cc9-414b-b84b-32fdd6054459","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Analysis complete notification sent","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Analysis completion notification sent","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"info","message":"Assessment job processed successfully","processingTime":"82223ms","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"a3329dc9-5ed1-4bb2-961a-5a8a4004fb1f","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Analysis result saved successfully","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Analysis result saved to Archive Service","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Analysis result saved successfully","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Analysis result saved to Archive Service","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"error","message":"Failed to update assessment job status","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Assessment job status updated","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Analysis complete notification sent","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Analysis completion notification sent","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"info","message":"Assessment job processed successfully","processingTime":"82069ms","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"5af7ee7a-b214-4873-9cb9-49c94b08f3fa","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"error","message":"Failed to update assessment job status","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Assessment job status updated","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Analysis complete notification sent","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Analysis completion notification sent","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"info","message":"Assessment job processed successfully","processingTime":"82000ms","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"65522994-979a-44ae-b0f3-d722f4d46e63","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Analysis result saved successfully","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Analysis result saved to Archive Service","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Analysis result saved successfully","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Analysis result saved to Archive Service","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"error","message":"Failed to update assessment job status","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Assessment job status updated","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Analysis complete notification sent","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Analysis completion notification sent","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"info","message":"Assessment job processed successfully","processingTime":"82080ms","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b5e44880-d7e5-4bad-8361-691af7a0fe92","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"error","message":"Failed to update assessment job status","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Assessment job status updated","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Analysis complete notification sent","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Analysis completion notification sent","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"info","message":"Assessment job processed successfully","processingTime":"82067ms","resultId":"a68e5372-209b-4242-a7d5-191581184234","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"3db6abef-97eb-4b3b-ad80-2117863e295f","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:28","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Analysis result saved successfully","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Analysis result saved to Archive Service","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Analysis result saved successfully","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Analysis result saved to Archive Service","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"error","message":"Failed to update assessment job status","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Assessment job status updated","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Analysis complete notification sent","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Analysis completion notification sent","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"info","message":"Assessment job processed successfully","processingTime":"82052ms","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"651346bc-c05d-4682-9c92-f7aed11e551b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"error","message":"Failed to update assessment job status","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Assessment job status updated","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Analysis complete notification sent","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Analysis completion notification sent","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"info","message":"Assessment job processed successfully","processingTime":"82052ms","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6184de28-bf64-49a2-92b7-8a5dd6c8e1fa","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Analysis result saved successfully","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Analysis result saved to Archive Service","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Analysis result saved successfully","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Analysis result saved to Archive Service","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"error","message":"Failed to update assessment job status","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Assessment job status updated","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Analysis complete notification sent","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Analysis completion notification sent","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"info","message":"Assessment job processed successfully","processingTime":"82120ms","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"31c32761-15d0-4978-af36-c2ebeba98cd8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"error","message":"Failed to update assessment job status","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Assessment job status updated","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Analysis complete notification sent","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Analysis completion notification sent","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"info","message":"Assessment job processed successfully","processingTime":"82155ms","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"bb48f030-c131-47d4-a8ae-2df799f3d5ae","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:28","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Analysis result saved successfully","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Analysis result saved to Archive Service","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Analysis result saved successfully","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Analysis result saved to Archive Service","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"error","message":"Failed to update assessment job status","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Assessment job status updated","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Analysis complete notification sent","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Analysis completion notification sent","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"info","message":"Assessment job processed successfully","processingTime":"82048ms","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"7d522b2e-964e-44b5-9790-d3fc0fabd20b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"error","message":"Failed to update assessment job status","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Assessment job status updated","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Analysis complete notification sent","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Analysis completion notification sent","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"info","message":"Assessment job processed successfully","processingTime":"82052ms","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"97e07a41-604c-45da-ad7a-60dbe756cee2","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Analysis result saved successfully","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Analysis result saved to Archive Service","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Analysis result saved successfully","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Analysis result saved to Archive Service","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"error","message":"Failed to update assessment job status","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Assessment job status updated","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Analysis complete notification sent","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Analysis completion notification sent","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"info","message":"Assessment job processed successfully","processingTime":"82048ms","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2a6dd7a2-a7ef-4984-8dc6-19087fd46e09","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"error","message":"Failed to update assessment job status","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Assessment job status updated","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Analysis complete notification sent","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Analysis completion notification sent","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"info","message":"Assessment job processed successfully","processingTime":"82044ms","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"f47a8746-f340-42ef-aca8-594872ca229e","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Analysis result saved successfully","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Analysis result saved to Archive Service","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Analysis result saved successfully","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Analysis result saved to Archive Service","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"error","message":"Failed to update assessment job status","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Assessment job status updated","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Analysis complete notification sent","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Analysis completion notification sent","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"info","message":"Assessment job processed successfully","processingTime":"82046ms","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"002e3fab-df12-4897-b7b8-46f956101f93","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"error","message":"Failed to update assessment job status","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Assessment job status updated","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Analysis complete notification sent","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Analysis completion notification sent","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"info","message":"Assessment job processed successfully","processingTime":"82017ms","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"4412e28b-cf50-4737-a82c-b32fa2abeec4","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:28","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Analysis result saved successfully","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Analysis result saved to Archive Service","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Analysis result saved successfully","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Analysis result saved to Archive Service","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"error","message":"Failed to update assessment job status","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Assessment job status updated","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Analysis complete notification sent","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Analysis completion notification sent","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"info","message":"Assessment job processed successfully","processingTime":"82075ms","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"a0f05fc4-6512-4441-84d1-bf21908f9a2f","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","useMockModel":true,"version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"error","message":"Failed to update assessment job status","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Assessment job status updated","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Analysis complete notification sent","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Analysis completion notification sent","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"info","message":"Assessment job processed successfully","processingTime":"82054ms","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"7d6b74ff-537b-4952-816b-34a0435ce2d2","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Analysis result saved successfully","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Analysis result saved to Archive Service","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Analysis result saved successfully","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Analysis result saved to Archive Service","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"error","message":"Failed to update assessment job status","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Assessment job status updated","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Analysis complete notification sent","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Analysis completion notification sent","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"info","message":"Assessment job processed successfully","processingTime":"82103ms","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"46461e66-ea49-45bb-9273-d39e9beb48d9","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","useMockModel":true,"version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"error","message":"Failed to update assessment job status","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Assessment job status updated","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Analysis complete notification sent","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Analysis completion notification sent","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"info","message":"Assessment job processed successfully","processingTime":"82080ms","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a6796668-23e7-408f-b65e-d40befa4a5d7","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Analysis result saved successfully","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Analysis result saved to Archive Service","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Analysis result saved successfully","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Analysis result saved to Archive Service","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"error","message":"Failed to update assessment job status","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Assessment job status updated","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Analysis complete notification sent","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Analysis completion notification sent","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"info","message":"Assessment job processed successfully","processingTime":"82059ms","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"e2868133-849d-44b0-8699-b55c81713638","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"error","message":"Failed to update assessment job status","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Assessment job status updated","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Analysis complete notification sent","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Analysis completion notification sent","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"info","message":"Assessment job processed successfully","processingTime":"81951ms","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"09ed9d2a-df05-4bc1-bd36-707c13ca80c9","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Analysis result saved successfully","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Analysis result saved to Archive Service","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Analysis result saved successfully","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Analysis result saved to Archive Service","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"error","message":"Failed to update assessment job status","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Assessment job status updated","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Analysis complete notification sent","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Analysis completion notification sent","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"info","message":"Assessment job processed successfully","processingTime":"82068ms","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"21c09ab9-045b-459a-aed3-9420701491c1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"error","message":"Failed to update assessment job status","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Assessment job status updated","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Analysis complete notification sent","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Analysis completion notification sent","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"info","message":"Assessment job processed successfully","processingTime":"82067ms","resultId":"5871010f-8f7e-4279-888c-493743489016","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b86f42f8-d088-47ea-abc1-ab14bad8f704","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Analysis result saved successfully","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Analysis result saved to Archive Service","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Analysis result saved successfully","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Analysis result saved to Archive Service","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"error","message":"Failed to update assessment job status","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Assessment job status updated","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Analysis complete notification sent","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Analysis completion notification sent","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"info","message":"Assessment job processed successfully","processingTime":"82086ms","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f50b9497-1769-473a-9d96-c854959436ce","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"error","message":"Failed to update assessment job status","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Assessment job status updated","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Analysis complete notification sent","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Analysis completion notification sent","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"info","message":"Assessment job processed successfully","processingTime":"82075ms","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"5c9e39bb-dd87-49fd-b0cb-51ec36f8242a","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:58","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:05","version":"1.0.0","weaknessesCount":3}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:05","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:05","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Analysis result saved successfully","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Analysis result saved to Archive Service","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Analysis result saved successfully","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Analysis result saved to Archive Service","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"error","message":"Failed to update assessment job status","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Assessment job status updated","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"error","message":"Failed to update assessment job status","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Assessment job status updated","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Analysis complete notification sent","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Analysis completion notification sent","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"info","message":"Assessment job processed successfully","processingTime":"82173ms","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"7208577b-68ea-4302-a9a2-8daafb2e4c2d","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Analysis complete notification sent","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Analysis completion notification sent","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"info","message":"Assessment job processed successfully","processingTime":"82151ms","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"b1188ed5-f38d-4f54-87a3-deb03f538cf0","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:09","version":"1.0.0","weaknessesCount":3}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Analysis result saved successfully","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:11","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Analysis result saved to Archive Service","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"error","message":"Failed to update assessment job status","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Assessment job status updated","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Analysis complete notification sent","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Analysis completion notification sent","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"info","message":"Assessment job processed successfully","processingTime":"82066ms","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"2d9523f2-2530-4e32-b7c0-1cf1c7d9bffe","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 05:44:52","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 05:44:53","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:53","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 05:44:53","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 05:44:53","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 05:44:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:45:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:46:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:47:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:48:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:49:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:50:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:51:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:52:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:53:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:54:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:55:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:56:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:57:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:58:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:59:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:00:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:01:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:02:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:03:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:04:53","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cfbe79df-c48b-4c90-a2c4-f10db6827799","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 06:04:54","userEmail":"<EMAIL>","userId":"217bc4f0-45b8-430c-883c-ecca47cdab45","version":"1.0.0"}
{"jobId":"cfbe79df-c48b-4c90-a2c4-f10db6827799","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 06:04:54","userEmail":"<EMAIL>","userId":"217bc4f0-45b8-430c-883c-ecca47cdab45","version":"1.0.0"}
{"jobId":"cfbe79df-c48b-4c90-a2c4-f10db6827799","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 06:04:54","version":"1.0.0"}
{"jobId":"cfbe79df-c48b-4c90-a2c4-f10db6827799","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 06:04:54","useMockModel":true,"version":"1.0.0"}
{"jobId":"cfbe79df-c48b-4c90-a2c4-f10db6827799","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 06:04:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 06:05:23","version":"1.0.0"}
